from flask import jsonify, request
from core.api import api_bp
from core.utils.auth_utils import auth_required
import sys
import os

# Add Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'Backend'))

from org_data import get_organization_data, save_organization_data
from archetype import get_brand_archetype_analysis, save_archetype_data
from brand_guidelines import get_brand_guidelines, save_brand_data


@api_bp.route('/hello', methods=['GET'])
@auth_required
def hello():
    return jsonify({'hello': 'world'})


@api_bp.route('/organization-data', methods=['POST'])
@auth_required
def analyze_organization():
    """
    POST API endpoint to analyze organization data from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "Domain": "...",
            "WhatWeDo": "...",
            "AboutUs": "...",
            "Class": "...",
            "url": "..."
        },
        "saved": true  // indicates if data was saved to storage
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process organization data
        org_data = get_organization_data(org_url)

        # Check if analysis was successful
        if org_data.get('Domain') == 'Error':
            return jsonify({
                "success": False,
                "error": "Failed to analyze organization data",
                "details": org_data.get('AboutUs', 'Unknown error occurred'),
                "data": org_data
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_organization_data(org_data, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": org_data,
            "saved": saved,
            "message": "Organization data analyzed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-archetype-analysis', methods=['POST'])
@auth_required
def analyze_brand_archetype():
    """
    POST API endpoint to analyze brand archetype from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "brand_personality_traits": [...],
            "core_values": [...],
            "archetype_scores": {
                "Hero": {"score": 85.5, "reasoning": "..."},
                "Sage": {"score": 72.3, "reasoning": "..."},
                ...
            },
            "primary_archetype": "Hero",
            "primary_reasoning": "...",
            "organization_url": "..."
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand archetype analysis
        archetype_analysis = get_brand_archetype_analysis(org_url)

        # Check if analysis was successful
        if archetype_analysis.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand archetype",
                "details": archetype_analysis.get('error', 'Unknown error occurred'),
                "data": archetype_analysis
            }), 500

        # Check for analysis errors based on content
        if (archetype_analysis.get('mission') == 'Error occurred during analysis' or
            archetype_analysis.get('primary_archetype') == 'Unknown'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand archetype analysis",
                "details": "Analysis returned incomplete or error results",
                "data": archetype_analysis
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_archetype_data(archetype_analysis, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": archetype_analysis,
            "saved": saved,
            "message": "Brand archetype analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-guidelines', methods=['POST'])
@auth_required
def analyze_brand_guidelines():
    """
    POST API endpoint to analyze brand guidelines from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "primary_color": "#1E88E5",
            "primary_color_reasoning": "Used prominently in header and main navigation",
            "secondary_color": "#FFC107",
            "secondary_color_reasoning": "Used for secondary buttons and highlights",
            "accent_color": "#4CAF50",
            "accent_color_reasoning": "Used for call-to-action elements and success states",
            "neutral_color": "#F5F5F5",
            "neutral_color_reasoning": "Used for backgrounds and subtle elements",
            "background_color": "#FFFFFF",
            "background_color_reasoning": "Main content area background",
            "text_color": "#212121",
            "text_color_reasoning": "Primary text color for readability",
            "has_gradient": true,
            "gradient_colors": "linear-gradient(45deg, #1E88E5, #42A5F5)",
            "gradient_direction": "45deg",
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "8px",
            "font": "Roboto",
            "font_size": "16px",
            "font_weight": "Normal",
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "organization_url": "...",
            "analysis_method": "screenshot_enhanced",
            "model_used": "gpt-4-vision-preview"
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand guidelines analysis
        brand_guidelines = get_brand_guidelines(org_url)

        # Check if analysis was successful
        if brand_guidelines.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand guidelines",
                "details": brand_guidelines.get('error', 'Unknown error occurred'),
                "data": brand_guidelines
            }), 500

        # Check for analysis errors based on content
        if (brand_guidelines.get('primary_color') == '#default' or
            brand_guidelines.get('primary_color_reasoning') == 'Analysis failed - using default value'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand guidelines analysis",
                "details": "Analysis returned incomplete or error results",
                "data": brand_guidelines
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            try:
                save_brand_data(brand_guidelines)
                saved = True
            except Exception as save_error:
                # Don't fail the entire request if saving fails
                saved = False

        # Return successful response
        return jsonify({
            "success": True,
            "data": brand_guidelines,
            "saved": saved,
            "message": "Brand guidelines analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500
