"""
Backend module for brand guidelines processing.

This module replicates the exact functionality from src/openengage/agents/brand_analyzer_updated.py
for extracting brand guidelines from websites.
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Tuple
from dotenv import load_dotenv
from openai import OpenAI
import base64

# Load environment variables
load_dotenv()


class WebsiteBrandAnalyzerTool:
    """Tool for extracting brand guidelines from websites."""

    def __init__(self):
        self.name = "Website Brand Analyzer"
        self.description = "Analyzes website HTML and CSS to extract brand guidelines and visual identity elements"

    def _extract_html_and_css(self, url: str) -> tuple:
        """Extract HTML content and CSS from a website."""
        try:
            # Send request with headers to mimic a browser
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()

            # Parse content
            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract inline CSS
            inline_css = ""
            for style in soup.find_all('style'):
                inline_css += style.string if style.string else ""

            # Extract linked CSS files
            linked_css = ""
            for link in soup.find_all('link', rel='stylesheet'):
                css_url = link.get('href')
                if css_url:
                    # Handle relative URLs
                    if css_url.startswith('/'):
                        base_url = '/'.join(url.split('/')[:3])  # http(s)://domain.com
                        css_url = base_url + css_url
                    elif not css_url.startswith(('http://', 'https://')):
                        css_url = url.rstrip('/') + '/' + css_url

                    try:
                        css_response = requests.get(css_url, headers=headers, timeout=5)
                        if css_response.status_code == 200:
                            linked_css += css_response.text
                    except:
                        pass  # Skip if CSS file can't be fetched

            # Extract inline styles from elements for gradient detection
            inline_styles = ""
            for element in soup.find_all(style=True):
                inline_styles += element.get('style', '') + " "

            # Combine all CSS
            combined_css = inline_css + "\n" + linked_css + "\n" + inline_styles

            return soup, combined_css

        except Exception as e:
            raise Exception(f"Failed to extract website content: {str(e)}")

    def _capture_screenshot(self, url: str, width: int = 1200, crop: int = 3000) -> Optional[str]:
        """Capture a screenshot of the website using thum.io service."""
        try:
            # Use thum.io service to capture screenshot
            thum_url = f"https://image.thum.io/get/width/{width}/crop/{crop}/{url}"

            response = requests.get(thum_url, timeout=30)
            response.raise_for_status()

            # Convert image to base64 for API usage
            image_base64 = base64.b64encode(response.content).decode('utf-8')
            return image_base64

        except Exception as e:
            print(f"Failed to capture screenshot: {str(e)}")
            return None

    def _build_user_content(self, url: str, html_sample: str, css_sample: str, text_content: str, screenshot_base64: Optional[str], has_gradient: bool = False, gradient_colors: str = None, gradient_direction: str = None):
        """Build user content for the API call, including screenshot if available."""
        if screenshot_base64:
            # Use vision model with both text and image
            return [
                {
                    "type": "text",
                    "text": f"""Analyze this website with URL: {url}

                    HTML Sample:
                    {html_sample}

                    CSS Sample:
                    {css_sample}

                    Text content sample (for mission/vision analysis):
                    {text_content[:3000]}

                    IMPORTANT: I've also provided a screenshot of the website. Please use this visual information
                    to accurately identify the colors, typography, and design elements as they actually appear
                    to users. Cross-reference the visual appearance with the CSS code to extract the most
                    accurate brand guidelines.

                    Extract the brand guidelines and return as JSON with these exact keys:
                    {{
                        "primary_color": "#hex",
                        "primary_color_reasoning": "Explain why this color was chosen as primary (e.g., most prominent in header, used for main buttons, brand logo color)",
                        "secondary_color": "#hex",
                        "secondary_color_reasoning": "Explain why this color was chosen as secondary (e.g., used in navigation, secondary buttons, complementary to primary)",
                        "accent_color": "#hex",
                        "accent_color_reasoning": "Explain why this color was chosen as accent (e.g., used for highlights, hover states, call-to-action elements)",
                        "neutral_color": "#hex",
                        "neutral_color_reasoning": "Explain why this color was chosen as neutral (e.g., used for borders, dividers, subtle backgrounds)",
                        "background_color": "#hex",
                        "background_color_reasoning": "Explain why this color was chosen as background (e.g., main page background, content area background)",
                        "text_color": "#hex",
                        "text_color_reasoning": "Explain why this color was chosen as text color (e.g., main body text, headings, readability contrast)",
                        "has_gradient": true/false,
                        "gradient_colors": "description of gradient colors or null",
                        "gradient_direction": "direction of gradient or null",
                        "cta_type": "Button or Link",
                        "cta_size": "Small/Medium/Large",
                        "button_style": "Rounded/Square/Pill",
                        "border_radius": "size with unit",
                        "font": "Font Used",
                        "font_size": "size with unit",
                        "font_weight": "Normal/Bold/etc",
                        "mission": "Mission statement if found",
                        "vision": "Vision statement if found",
                        "tone_of_voice": "3-4 brand tone of voice descriptors separated by commas"
                    }}

                    If you can't determine specific values, use sensible defaults but note them as 'default'.

                    For the gradient analysis, I've already detected: has_gradient={has_gradient}, gradient_colors='{gradient_colors}', gradient_direction='{gradient_direction}'. Use this information in your analysis."""
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{screenshot_base64}",
                        "detail": "high"
                    }
                }
            ]
        else:
            # Text-only analysis
            return f"""Analyze this website with URL: {url}

                    HTML Sample:
                    {html_sample}

                    CSS Sample:
                    {css_sample}

                    Text content sample (for mission/vision analysis):
                    {text_content[:3000]}

                    Extract the brand guidelines and return as JSON with these exact keys:
                    {{
                        "primary_color": "#hex",
                        "primary_color_reasoning": "Explain why this color was chosen as primary (e.g., most prominent in header, used for main buttons, brand logo color)",
                        "secondary_color": "#hex",
                        "secondary_color_reasoning": "Explain why this color was chosen as secondary (e.g., used in navigation, secondary buttons, complementary to primary)",
                        "accent_color": "#hex",
                        "accent_color_reasoning": "Explain why this color was chosen as accent (e.g., used for highlights, hover states, call-to-action elements)",
                        "neutral_color": "#hex",
                        "neutral_color_reasoning": "Explain why this color was chosen as neutral (e.g., used for borders, dividers, subtle backgrounds)",
                        "background_color": "#hex",
                        "background_color_reasoning": "Explain why this color was chosen as background (e.g., main page background, content area background)",
                        "text_color": "#hex",
                        "text_color_reasoning": "Explain why this color was chosen as text color (e.g., main body text, headings, readability contrast)",
                        "has_gradient": true/false,
                        "gradient_colors": "description of gradient colors or null",
                        "gradient_direction": "direction of gradient or null",
                        "cta_type": "Button or Link",
                        "cta_size": "Small/Medium/Large",
                        "button_style": "Rounded/Square/Pill",
                        "border_radius": "size with unit",
                        "font": "Font Used",
                        "font_size": "size with unit",
                        "font_weight": "Normal/Bold/etc",
                        "mission": "Mission statement if found",
                        "vision": "Vision statement if found",
                        "tone_of_voice": "3-4 brand tone of voice descriptors separated by commas"
                    }}

                    If you can't determine specific values, use sensible defaults but note them as 'default'.

                    For the gradient analysis, I've already detected: has_gradient={has_gradient}, gradient_colors='{gradient_colors}', gradient_direction='{gradient_direction}'. Use this information in your analysis."""

    def _execute(self, url: str) -> Dict[str, Any]:
        """Execute the brand analysis on the given website URL."""
        try:
            # Extract HTML and CSS
            soup, css = self._extract_html_and_css(url)

            # Capture website screenshot
            print(f"Capturing screenshot for {url}...")
            screenshot_base64 = self._capture_screenshot(url)
            if screenshot_base64:
                print("✓ Screenshot captured successfully")
            else:
                print("⚠ Screenshot capture failed, proceeding with text-only analysis")

            # Get text content for mission/vision analysis
            text_content = soup.get_text()

            # Prepare prompt with HTML, CSS, and extracted text
            html_sample = str(soup.prettify())[:3000]  # First 3000 chars of HTML
            css_sample = css[:3000]  # First 3000 chars of CSS

            # Use OpenAI to analyze the content
            client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

            # Pre-analyze CSS for gradient patterns
            has_gradient = False
            gradient_colors = None
            gradient_direction = None
            gradient_details = None

            # Look for gradients in CSS
            gradient_patterns = [
                r'linear-gradient\s*\([^)]+\)',
                r'radial-gradient\s*\([^)]+\)',
                r'conic-gradient\s*\([^)]+\)',
                r'repeating-linear-gradient\s*\([^)]+\)',
                r'repeating-radial-gradient\s*\([^)]+\)'
            ]

            all_gradients = []
            for pattern in gradient_patterns:
                gradient_matches = re.findall(pattern, css)
                all_gradients.extend(gradient_matches)

            # Sort by length to find the most complex/detailed gradient
            all_gradients.sort(key=len, reverse=True)

            if all_gradients:
                has_gradient = True
                gradient_colors = all_gradients[0]  # Get the most complex match

                # Try to extract direction
                direction_match = re.search(r'(?:to\s+(?:top|bottom|left|right|top\s+left|top\s+right|bottom\s+left|bottom\s+right)|\d+deg)', gradient_colors)
                if direction_match:
                    gradient_direction = direction_match.group(0)
                else:
                    gradient_direction = "not specified"

                # Extract all hex colors in the gradient
                hex_colors = re.findall(r'#[0-9a-fA-F]{3,8}', gradient_colors)
                if hex_colors:
                    gradient_details = ", ".join(hex_colors)

                # Also look for gradients in element backgrounds
                print(f"Found gradient: {gradient_colors}")
                print(f"Direction: {gradient_direction}")
                print(f"Colors: {gradient_details}")

            # Choose model based on whether we have a screenshot
            model = "gpt-4o" if screenshot_base64 else "gpt-4o-mini-2024-07-18"
            print(f"Using model: {model} {'(with screenshot analysis)' if screenshot_base64 else '(text-only analysis)'}")

            response = client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": """You are a brand identity expert who analyzes websites to extract branding guidelines.
                        Look for patterns in colors, typography, button styles, and company information.

                        You will analyze both the HTML/CSS code AND a screenshot of the website to extract:
                        1. Color scheme: primary, secondary, accent, neutral, background, and text colors (as hex codes)
                        2. Color reasoning: For EACH color, provide specific reasoning explaining WHERE and WHY that color was chosen
                        3. Gradient colors: look for linear-gradient, radial-gradient, etc. in CSS
                        4. Button/CTA styling: type (Button/Link), size (Small/Medium/Large), style (Rounded/Square/Pill), border radius
                        5. Typography: It is compulsory that you have to choose a font from one of ["Arial", "Helvetica", "Roboto", "Open Sans", "Montserrat", "Lato", "Georgia", "Times New Roman"]), size in px, weight
                        6. Brand identity: mission, vision, tone of voice (if available)

                        CRITICAL FOR COLOR ANALYSIS:
                        - Use the screenshot to visually verify and identify the actual colors being used on the website
                        - The screenshot shows the real rendered appearance, which may differ from CSS values due to overlays, opacity, or dynamic styling
                        - Cross-reference the visual colors in the screenshot with the CSS to determine the most accurate brand colors
                        - For EACH color you identify, provide specific reasoning based on WHERE you see it used (e.g., "Used in the main navigation bar", "Primary button color throughout the site", "Main heading text color")
                        - Be specific about the visual elements that led to your color choice (e.g., "This color appears in the logo, main CTA buttons, and active navigation items")
                        - If you cannot clearly identify a specific color usage, explain your reasoning for the default choice

                        For colors, prioritize what you see in the screenshot over CSS values when there are discrepancies.
                        Look for the most prominent and consistently used colors in headers, buttons, backgrounds, and text.
                        For missing values, provide reasonable defaults based on common web design patterns but explain your reasoning.

                        Analyze the website to extract brand identity elements like mission, vision, and tone of voice.
                        """
                    },
                    {
                        "role": "user",
                        "content": self._build_user_content(url, html_sample, css_sample, text_content, screenshot_base64, has_gradient, gradient_colors, gradient_direction)
                    }
                ],
                temperature=0.3,
                max_tokens=1024,
                response_format={"type": "json_object"}
            )

            response_content = response.choices[0].message.content
            print(f"Raw API response (first 500 chars): {response_content[:500]}...")
            parsed_response = json.loads(response_content)

            # Assemble return data with brand guidelines and additional extracted info
            return_data = {
                "primary_color": parsed_response.get("primary_color", ""),
                "primary_color_reasoning": parsed_response.get("primary_color_reasoning", "Default primary color - no specific usage identified"),
                "secondary_color": parsed_response.get("secondary_color", ""),
                "secondary_color_reasoning": parsed_response.get("secondary_color_reasoning", "Default secondary color - no specific usage identified"),
                "accent_color": parsed_response.get("accent_color", ""),
                "accent_color_reasoning": parsed_response.get("accent_color_reasoning", "Default accent color - no specific usage identified"),
                "neutral_color": parsed_response.get("neutral_color", "#FFFFFF"),
                "neutral_color_reasoning": parsed_response.get("neutral_color_reasoning", "Default neutral color for backgrounds and subtle elements"),
                "background_color": parsed_response.get("background_color", "#F5F5F5"),
                "background_color_reasoning": parsed_response.get("background_color_reasoning", "Default background color for main content areas"),
                "text_color": parsed_response.get("text_color", "#212121"),
                "text_color_reasoning": parsed_response.get("text_color_reasoning", "Default text color for readability and contrast"),
                "cta_type": parsed_response.get("cta_type", "Button"),
                "// Commonly used for call-to-action elements": parsed_response.get("cta_type", "Button"),
                "cta_size": parsed_response.get("cta_size", "Medium"),
                "// Default size for buttons, can be adjusted based on actual usage": parsed_response.get("cta_size", "Medium"),
                "button_style": parsed_response.get("button_style", "Rounded"),
                "// Default button style, can be adjusted based on actual usage": parsed_response.get("button_style", "Rounded"),
                "border_radius": parsed_response.get("border_radius", "8px"),
                "// Default border radius for buttons, can be adjusted based on actual usage": parsed_response.get("border_radius", "8px"),
                "font": parsed_response.get("font", ""),
                "// Extracted font family from CSS": "",
                "font_size": parsed_response.get("font_size", "16px"),
                "// Default font size, can be adjusted based on actual usage": parsed_response.get("font_size", "16px"),
                "font_weight": parsed_response.get("font_weight", "Normal"),
                "// Default font weight, can be adjusted based on actual usage": parsed_response.get("font_weight", "Normal"),
                "mission": parsed_response.get("mission", ""),
                "vision": parsed_response.get("vision", ""),
                "tone_of_voice": parsed_response.get("tone_of_voice", ""),
                "organization_url": url,
                "analysis_method": "screenshot_enhanced" if screenshot_base64 else "text_only",
                "model_used": model
            }

            # Add gradient information if found
            if has_gradient and gradient_colors:
                return_data["has_gradient"] = True
                return_data["gradient_colors"] = gradient_colors
                if gradient_direction:
                    return_data["gradient_direction"] = gradient_direction
                if gradient_details:
                    return_data["gradient_details"] = gradient_details

            return return_data

        except Exception as e:
            # Return default guidelines if analysis fails
            default_guidelines = {
                "primary_color": "#default",
                "primary_color_reasoning": "Analysis failed - using default value",
                "secondary_color": "#default",
                "secondary_color_reasoning": "Analysis failed - using default value",
                "accent_color": "#default",
                "accent_color_reasoning": "Analysis failed - using default value",
                "neutral_color": "#FFFFFF",
                "neutral_color_reasoning": "Analysis failed - using default value",
                "background_color": "#F5F5F5",
                "background_color_reasoning": "Analysis failed - using default value",
                "text_color": "#212121",
                "text_color_reasoning": "Analysis failed - using default value",
                "cta_type": "Button",
                "cta_size": "Medium",
                "button_style": "Rounded",
                "border_radius": "8px",
                "font": "",
                "font_size": "16px",
                "font_weight": "Normal",
                "mission": None,
                "vision": None,
                "tone_of_voice": None,
                "error": str(e)
            }
            return default_guidelines

    def _run(self, url: str) -> Dict[str, Any]:
        """Run the tool with the given URL."""
        return self._execute(url)


def get_brand_guidelines(org_url: str) -> Dict[str, Any]:
    """
    Main function to get brand guidelines for an organization.

    Args:
        org_url (str): Organization website URL

    Returns:
        Dict[str, Any]: Brand guidelines data
    """
    analyzer = WebsiteBrandAnalyzerTool()
    return analyzer._execute(org_url)


def save_brand_data(guidelines: Dict[str, Any], filename: str = "brand_guidelines.json") -> None:
    """
    Save brand guidelines to JSON file.

    Args:
        guidelines (Dict[str, Any]): Brand guidelines data
        filename (str): Output filename
    """
    # Create data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    os.makedirs(data_dir, exist_ok=True)

    filepath = os.path.join(data_dir, filename)

    # Load existing data if file exists
    existing_data = {}
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except:
            existing_data = {}

    # Add new data
    org_url = guidelines.get('organization_url', 'default_organization')
    existing_data[org_url] = guidelines

    # Save updated data
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(existing_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Brand guidelines saved to {filepath}")


def test_brand_analysis(test_url: str = "https://www.analyticsvidhya.com/") -> None:
    """
    Test function to demonstrate brand analysis.

    Args:
        test_url (str): URL to test with (defaults to Analytics Vidhya)
    """
    print(f"Testing brand analysis for: {test_url}")
    print("-" * 50)

    # Get brand guidelines
    guidelines = get_brand_guidelines(test_url)

    # Display results
    print("Brand Guidelines Analysis Results:")
    print(f"Primary Color: {guidelines.get('primary_color', 'N/A')}")
    print(f"Secondary Color: {guidelines.get('secondary_color', 'N/A')}")
    print(f"Accent Color: {guidelines.get('accent_color', 'N/A')}")
    print(f"Has Gradient: {guidelines.get('has_gradient', False)}")
    print(f"CTA Type: {guidelines.get('cta_type', 'N/A')}")
    print(f"Button Style: {guidelines.get('button_style', 'N/A')}")
    print(f"Font: {guidelines.get('font', 'N/A')}")
    print(f"Mission: {guidelines.get('mission', 'N/A')}")
    print(f"Vision: {guidelines.get('vision', 'N/A')}")
    print(f"Tone of Voice: {guidelines.get('tone_of_voice', 'N/A')}")

    # Save the results
    save_brand_data(guidelines)


if __name__ == "__main__":
    test_brand_analysis()