"""
Backend module for product details processing.

This module compiles all the product-related functionality from multiple scripts
into a single backend service. It handles product URL processing, feature extraction,
summary generation, and batch product analysis.

Functions:
- get_product_details(product_urls): Main function that takes product URLs and returns product details
- scrape_product_content(url): Scrapes content from product website
- analyze_product_details(content): Analyzes scraped content to extract product info
- process_multiple_products(urls): Processes multiple product URLs in batch
- save_product_data(products): Saves product data to storage
- load_product_data(): Loads existing product data
"""

import os
import json
import re
import requests
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional, List, Union
from dotenv import load_dotenv
from openai import OpenAI
from datetime import datetime

# Load environment variables
load_dotenv()


class WebScrapingTool:
    """Tool for scraping web content from product websites."""
    
    def __init__(self):
        self.name = "Web Scraper"
        self.description = "Scrapes content from a given URL"
    
    def scrape_content(self, url: str) -> str:
        """
        Scrape content from the given URL.
        
        Args:
            url (str): The URL to scrape
            
        Returns:
            str: Scraped text content from the website
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
                
            # Get text content
            text = soup.get_text(separator='\n', strip=True)
            
            # Clean up text
            lines = [line.strip() for line in text.splitlines() if line.strip()]
            content = '\n'.join(lines)
            
            return content
            
        except Exception as e:
            return f"Error scraping URL: {str(e)}"


class ProductAnalyzerTool:
    """Tool for analyzing product details from website content."""
    
    def __init__(self):
        self.name = "Product Analyzer"
        self.description = "Analyzes product details from scraped content"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def analyze_content(self, content: str) -> Dict[str, Any]:
        """
        Analyze website content to extract product details.
        
        Args:
            content (str): Scraped website content
            
        Returns:
            Dict[str, Any]: Dictionary containing product details
        """
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a product analyzer. Extract the following details from the provided content:
                        - Product Name
                        - Company Name
                        - Type of Product
                        - Key Features (as a list)
                        - Detailed Product Summary in 200 words.
                        
                        Return the information in a JSON format with these exact keys:
                        {
                            "Product_Name": "...",
                            "Company_Name": "...",
                            "Type_of_Product": "...",
                            "Product_Features": ["...", "..."],
                            "Product_Summary": "..."
                        }
                        
                        If any field is not found, use reasonable defaults based on available information.
                        Never return null or empty values."""
                    },
                    {
                        "role": "user",
                        "content": f"Extract structured product details from the following content:\n{content}"
                    }
                ],
                temperature=0.9
            )
            
            try:
                json_str = response.choices[0].message.content.strip()
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                json_output = json.loads(json_str)
                
                # Set default values for missing fields
                default_output = {
                    "Product_Name": "Unknown Product",
                    "Company_Name": "Unknown Company",
                    "Type_of_Product": "Unknown Type",
                    "Product_Features": ["No features available"],
                    "Product_Summary": "No summary available"
                }
                
                for key in default_output:
                    if key not in json_output or not json_output[key]:
                        json_output[key] = default_output[key]
                
                # Ensure Product_Features is a list
                if not isinstance(json_output["Product_Features"], list):
                    if isinstance(json_output["Product_Features"], str):
                        json_output["Product_Features"] = [json_output["Product_Features"]]
                    else:
                        json_output["Product_Features"] = ["No features available"]
                
                return json_output
                
            except (json.JSONDecodeError, AttributeError) as e:
                return {
                    "Product_Name": "Error Processing Product",
                    "Company_Name": "Unknown Company",
                    "Type_of_Product": "Unknown Type",
                    "Product_Features": ["Error processing features"],
                    "Product_Summary": f"Error processing product details: {str(e)}"
                }
            
        except Exception as e:
            return {
                "Product_Name": "Error Processing Product",
                "Company_Name": "Error",
                "Type_of_Product": "Error",
                "Product_Features": ["Error occurred"],
                "Product_Summary": f"Failed to analyze product: {str(e)}"
            }


def scrape_product_content(url: str) -> str:
    """
    Scrape content from product website.
    
    Args:
        url (str): Product website URL
        
    Returns:
        str: Scraped website content
    """
    scraper = WebScrapingTool()
    return scraper.scrape_content(url)


def analyze_product_details(content: str) -> Dict[str, Any]:
    """
    Analyze scraped content to extract product information.
    
    Args:
        content (str): Scraped website content
        
    Returns:
        Dict[str, Any]: Product details including Name, Features, Summary, Type, Company
    """
    analyzer = ProductAnalyzerTool()
    return analyzer.analyze_content(content)


def get_single_product_details(product_url: str, organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Get product details for a single product URL.
    
    Args:
        product_url (str): Product website URL
        organization_url (str, optional): Organization URL to associate with the product
        
    Returns:
        Dict[str, Any]: Complete product data
    """
    try:
        # Step 1: Scrape product content
        content = scrape_product_content(product_url)
        
        if content.startswith("Error scraping URL:"):
            return {
                "Product_Name": "Error",
                "Company_Name": "Could not scrape website",
                "Type_of_Product": "Error",
                "Product_Features": [content],
                "Product_Summary": content,
                "Product_URL": product_url,
                "Company_URL": organization_url or "",
                "organization_url": organization_url or "",
                "Priority": 1,
                "Collaterals": []
            }
        
        # Step 2: Analyze product details
        product_data = analyze_product_details(content)
        
        # Step 3: Add URLs to the product data
        product_data["Product_URL"] = product_url
        if organization_url:
            product_data["Company_URL"] = organization_url
            product_data["organization_url"] = organization_url
        
        # Step 4: Add optional fields that may be used in the system
        # Priority field (used for product ranking)
        if "Priority" not in product_data:
            product_data["Priority"] = 1  # Default priority

        # Collaterals field (used for uploaded files)
        if "Collaterals" not in product_data:
            product_data["Collaterals"] = []
        
        return product_data
        
    except Exception as e:
        return {
            "Product_Name": "Error",
            "Company_Name": "Error occurred during analysis",
            "Type_of_Product": "Error",
            "Product_Features": [f"Failed to analyze product: {str(e)}"],
            "Product_Summary": f"Failed to analyze product: {str(e)}",
            "Product_URL": product_url,
            "Company_URL": organization_url or "",
            "organization_url": organization_url or "",
            "Priority": 1,
            "Collaterals": []
        }


def process_multiple_products(product_urls: List[str], organization_url: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Process multiple product URLs and return their details.
    
    Args:
        product_urls (List[str]): List of product URLs to analyze
        organization_url (str, optional): Organization URL to associate with products
        
    Returns:
        List[Dict[str, Any]]: List of product details
    """
    analyzed_products = []
    
    for url in product_urls:
        print(f"Analyzing product: {url}")
        product_data = get_single_product_details(url, organization_url)
        analyzed_products.append(product_data)
    
    return analyzed_products


def get_product_details(product_urls: Union[str, List[str]], organization_url: Optional[str] = None) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Main function that takes product URL(s) and returns complete product details.

    This function:
    1. Handles both single URL string and list of URLs
    2. Scrapes content from each product website
    3. Analyzes the content using AI to extract product details
    4. Returns structured product data

    Args:
        product_urls (Union[str, List[str]]): Single product URL or list of product URLs
        organization_url (str, optional): Organization URL to associate with products

    Returns:
        Union[Dict[str, Any], List[Dict[str, Any]]]: Product data or list of product data containing:
            - Product_Name: Name of the product
            - Company_Name: Company that makes the product
            - Type_of_Product: Category/type of the product
            - Product_Features: List of key features
            - Product_Summary: Detailed product description
            - Product_URL: Original product URL
            - Company_URL: Organization URL (if provided)
            - organization_url: Organization URL (if provided)
            - Priority: Product priority ranking (default: 1)
            - Collaterals: Array for uploaded collateral files (default: [])
    """
    # Handle single URL string
    if isinstance(product_urls, str):
        return get_single_product_details(product_urls, organization_url)

    # Handle list of URLs
    elif isinstance(product_urls, list):
        return process_multiple_products(product_urls, organization_url)

    else:
        raise ValueError("product_urls must be a string or list of strings")


# Data management functions
def save_product_data(product_data: Union[Dict[str, Any], List[Dict[str, Any]]], replace_all: bool = False) -> bool:
    """
    Save product data to the data storage file.

    Args:
        product_data (Union[Dict, List[Dict]]): Single product dict or list of product dicts
        replace_all (bool): If True, replace all products with the provided data

    Returns:
        bool: True if save successful, False otherwise
    """
    if not product_data:
        return False

    try:
        # Handle both single product and list of products
        if isinstance(product_data, list):
            products_to_save = product_data
        else:
            products_to_save = [product_data]

        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
        data_file_path = 'data/product_details.json'

        if replace_all:
            # Replace all products with new data
            final_products = products_to_save
        else:
            # Load existing products and merge
            existing_products = load_product_data()

            # Process each product to save
            final_products = existing_products.copy()
            for new_product in products_to_save:
                # Check if product already exists
                found = False
                product_name = new_product.get("Product_Name")

                for i, existing_product in enumerate(final_products):
                    if existing_product.get("Product_Name") == product_name:
                        # Update existing product
                        final_products[i] = new_product
                        found = True
                        break

                # If not found, append new product
                if not found:
                    final_products.append(new_product)

        # Save updated list
        with open(data_file_path, 'w') as f:
            json.dump(final_products, f, indent=4)

        return True

    except Exception as e:
        print(f"Error saving product data: {str(e)}")
        return False


def load_product_data(product_name: Optional[str] = None, organization_url: Optional[str] = None, filter_by_org: bool = False) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Load product data from storage.

    Args:
        product_name (str, optional): Name of specific product to load
        organization_url (str, optional): URL of organization to filter by
        filter_by_org (bool): Whether to filter products by organization URL

    Returns:
        Union[Dict, List[Dict]]: Product data or list of product data
    """
    try:
        data_file_path = 'data/product_details.json'

        if not os.path.exists(data_file_path):
            return {} if product_name else []

        with open(data_file_path, 'r') as f:
            products_list = json.load(f)
            if not isinstance(products_list, list):
                products_list = [products_list]  # Convert old format to list

            # Filter by organization URL if requested
            if filter_by_org and organization_url:
                # Check both Company_URL and organization URL in product data
                products_list = [p for p in products_list if p.get("Company_URL", "") == organization_url or
                                                            p.get("organization_url", "") == organization_url]

            if product_name:
                # Find details for specific product
                for product in products_list:
                    if product.get("Product_Name") == product_name:
                        return product
                return {}  # Product not found

            # Return all products
            return products_list

    except (FileNotFoundError, json.JSONDecodeError):
        return {} if product_name else []


def get_all_products(organization_url: Optional[str] = None, filter_by_org: bool = False) -> List[Dict[str, Any]]:
    """
    Get all products, optionally filtered by organization.

    Args:
        organization_url (str, optional): URL of organization to filter by
        filter_by_org (bool): Whether to filter products by organization URL

    Returns:
        List[Dict[str, Any]]: List of all product dictionaries
    """
    return load_product_data(organization_url=organization_url, filter_by_org=filter_by_org)


def update_product_data(updated_product: Dict[str, Any]) -> bool:
    """
    Update a specific product in the data storage.

    Args:
        updated_product (Dict[str, Any]): Updated product data

    Returns:
        bool: True if update successful, False otherwise
    """
    return save_product_data(updated_product, replace_all=False)


def delete_product_data(product_name: str) -> bool:
    """
    Delete a specific product from the data storage.

    Args:
        product_name (str): Name of the product to delete

    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        products_list = load_product_data()

        # Filter out the product to delete
        updated_products = [p for p in products_list if p.get("Product_Name") != product_name]

        # Save the updated list
        return save_product_data(updated_products, replace_all=True)

    except Exception as e:
        print(f"Error deleting product data: {str(e)}")
        return False


# Utility functions
def parse_product_urls(url_input: str) -> List[str]:
    """
    Parse product URLs from various input formats.

    Args:
        url_input (str): Input string containing URLs (comma-separated, space-separated, or newline-separated)

    Returns:
        List[str]: List of cleaned URLs
    """
    # Split by common separators
    urls = re.split(r'[,\s\n]+', url_input.strip())

    # Clean and validate URLs
    cleaned_urls = []
    for url in urls:
        url = url.strip()
        if url and (url.startswith('http://') or url.startswith('https://')):
            cleaned_urls.append(url)

    return cleaned_urls


def validate_product_data(product_data: Dict[str, Any]) -> bool:
    """
    Validate product data structure.

    Args:
        product_data (Dict[str, Any]): Product data to validate

    Returns:
        bool: True if valid, False otherwise
    """
    required_fields = ["Product_Name", "Company_Name", "Type_of_Product", "Product_Features", "Product_Summary"]

    for field in required_fields:
        if field not in product_data or not product_data[field]:
            return False

    # Ensure Product_Features is a list
    if not isinstance(product_data["Product_Features"], list):
        return False

    return True


# Example usage and testing function
def test_product_analysis(test_urls: Union[str, List[str]] = "https://www.analyticsvidhya.com/pinnacleplus/") -> None:
    """
    Test function to demonstrate product details extraction.

    Args:
        test_urls (Union[str, List[str]]): URL(s) to test with
    """
    print(f"Testing product analysis for: {test_urls}")
    print("-" * 50)

    # Get product details
    product_data = get_product_details(test_urls)

    # Display results
    if isinstance(product_data, list):
        print(f"Analyzed {len(product_data)} products:")
        for i, product in enumerate(product_data, 1):
            print(f"\nProduct {i}:")
            print(f"Name: {product.get('Product_Name', 'N/A')}")
            print(f"Company: {product.get('Company_Name', 'N/A')}")
            print(f"Type: {product.get('Type_of_Product', 'N/A')}")
            print(f"Features: {product.get('Product_Features', [])}")
            print(f"Summary: {product.get('Product_Summary', 'N/A')[:100]}...")
    else:
        print("Product Analysis Results:")
        print(f"Name: {product_data.get('Product_Name', 'N/A')}")
        print(f"Company: {product_data.get('Company_Name', 'N/A')}")
        print(f"Type: {product_data.get('Type_of_Product', 'N/A')}")
        print(f"Features: {product_data.get('Product_Features', [])}")
        print(f"Summary: {product_data.get('Product_Summary', 'N/A')}")

    # Save the data
    if save_product_data(product_data):
        print("\n✅ Product data saved successfully!")
    else:
        print("\n❌ Failed to save product data!")


if __name__ == "__main__":
    # Test the module
    test_product_analysis()
