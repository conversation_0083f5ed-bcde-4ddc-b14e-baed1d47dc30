from functools import wraps
from flask import request, jsonify, current_app


def auth_required(f):

    @wraps(f)
    def decorated(*args, **kwargs):

        token = request.headers.get('X-API-Token')
        if not token:
            return jsonify({"error": "Token is missing"}), 401

        if token != current_app.config['API_TOKEN']:
            return jsonify({"error": "Token is invalid"}), 401

        return f(*args, **kwargs)

    return decorated
