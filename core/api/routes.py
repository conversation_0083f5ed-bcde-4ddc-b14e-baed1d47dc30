from flask import jsonify, request
from core.api import api_bp
from core.utils.auth_utils import auth_required
import sys
import os

# Add Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'Backend'))

from org_data import get_organization_data, save_organization_data
from archetype import get_brand_archetype_analysis, save_archetype_data
from brand_guidelines import get_brand_guidelines, save_brand_data
from image_scraper import scrape_images, get_image_statistics, validate_url, estimate_scraping_time, get_supported_image_types


@api_bp.route('/hello', methods=['GET'])
@auth_required
def hello():
    return jsonify({'hello': 'world'})


@api_bp.route('/organization-data', methods=['POST'])
@auth_required
def analyze_organization():
    """
    POST API endpoint to analyze organization data from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "Domain": "...",
            "WhatWeDo": "...",
            "AboutUs": "...",
            "Class": "...",
            "url": "..."
        },
        "saved": true  // indicates if data was saved to storage
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process organization data
        org_data = get_organization_data(org_url)

        # Check if analysis was successful
        if org_data.get('Domain') == 'Error':
            return jsonify({
                "success": False,
                "error": "Failed to analyze organization data",
                "details": org_data.get('AboutUs', 'Unknown error occurred'),
                "data": org_data
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_organization_data(org_data, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": org_data,
            "saved": saved,
            "message": "Organization data analyzed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-archetype-analysis', methods=['POST'])
@auth_required
def analyze_brand_archetype():
    """
    POST API endpoint to analyze brand archetype from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "brand_personality_traits": [...],
            "core_values": [...],
            "archetype_scores": {
                "Hero": {"score": 85.5, "reasoning": "..."},
                "Sage": {"score": 72.3, "reasoning": "..."},
                ...
            },
            "primary_archetype": "Hero",
            "primary_reasoning": "...",
            "organization_url": "..."
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand archetype analysis
        archetype_analysis = get_brand_archetype_analysis(org_url)

        # Check if analysis was successful
        if archetype_analysis.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand archetype",
                "details": archetype_analysis.get('error', 'Unknown error occurred'),
                "data": archetype_analysis
            }), 500

        # Check for analysis errors based on content
        if (archetype_analysis.get('mission') == 'Error occurred during analysis' or
            archetype_analysis.get('primary_archetype') == 'Unknown'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand archetype analysis",
                "details": "Analysis returned incomplete or error results",
                "data": archetype_analysis
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            saved = save_archetype_data(archetype_analysis, org_url)

        # Return successful response
        return jsonify({
            "success": True,
            "data": archetype_analysis,
            "saved": saved,
            "message": "Brand archetype analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/brand-guidelines', methods=['POST'])
@auth_required
def analyze_brand_guidelines():
    """
    POST API endpoint to analyze brand guidelines from a website URL.

    Expected JSON payload:
    {
        "org_url": "https://example.com",
        "save_data": true  // optional, defaults to true
    }

    Returns:
    {
        "success": true,
        "data": {
            "primary_color": "#1E88E5",
            "primary_color_reasoning": "Used prominently in header and main navigation",
            "secondary_color": "#FFC107",
            "secondary_color_reasoning": "Used for secondary buttons and highlights",
            "accent_color": "#4CAF50",
            "accent_color_reasoning": "Used for call-to-action elements and success states",
            "neutral_color": "#F5F5F5",
            "neutral_color_reasoning": "Used for backgrounds and subtle elements",
            "background_color": "#FFFFFF",
            "background_color_reasoning": "Main content area background",
            "text_color": "#212121",
            "text_color_reasoning": "Primary text color for readability",
            "has_gradient": true,
            "gradient_colors": "linear-gradient(45deg, #1E88E5, #42A5F5)",
            "gradient_direction": "45deg",
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "8px",
            "font": "Roboto",
            "font_size": "16px",
            "font_weight": "Normal",
            "mission": "...",
            "vision": "...",
            "tone_of_voice": "...",
            "organization_url": "...",
            "analysis_method": "screenshot_enhanced",
            "model_used": "gpt-4-vision-preview"
        },
        "saved": true
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        org_url = data.get('org_url')
        if not org_url:
            return jsonify({
                "success": False,
                "error": "Missing required field: org_url"
            }), 400

        # Validate URL format (basic validation)
        if not org_url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get save_data flag (defaults to True)
        save_data = data.get('save_data', True)

        # Process brand guidelines analysis
        brand_guidelines = get_brand_guidelines(org_url)

        # Check if analysis was successful
        if brand_guidelines.get('error'):
            return jsonify({
                "success": False,
                "error": "Failed to analyze brand guidelines",
                "details": brand_guidelines.get('error', 'Unknown error occurred'),
                "data": brand_guidelines
            }), 500

        # Check for analysis errors based on content
        if (brand_guidelines.get('primary_color') == '#default' or
            brand_guidelines.get('primary_color_reasoning') == 'Analysis failed - using default value'):
            return jsonify({
                "success": False,
                "error": "Failed to complete brand guidelines analysis",
                "details": "Analysis returned incomplete or error results",
                "data": brand_guidelines
            }), 500

        # Save data if requested
        saved = False
        if save_data:
            try:
                save_brand_data(brand_guidelines)
                saved = True
            except Exception as save_error:
                # Don't fail the entire request if saving fails
                saved = False

        # Return successful response
        return jsonify({
            "success": True,
            "data": brand_guidelines,
            "saved": saved,
            "message": "Brand guidelines analysis completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scrape-images', methods=['POST'])
@auth_required
def scrape_website_images():
    """
    POST API endpoint to scrape images from a website URL.

    Expected JSON payload:
    {
        "url": "https://example.com",
        "min_pixels": 100,  // optional, defaults to 100
        "num_images": 10,   // optional, no limit if not specified
        "image_types": ["jpg", "jpeg", "png", "webp"],  // optional, defaults to all supported types
        "usage": "Product Images"  // optional, defaults to "All Products"
    }

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 5,
            "images_data": [
                {
                    "id": "uuid-string",
                    "filename": "scraped_image_1.jpg",
                    "original_filename": "product.jpg",
                    "name": "product.jpg",
                    "upload_date": "2024-07-25T10:30:00",
                    "categories": [],
                    "usage": "Product Images",
                    "notes": "",
                    "width": 800,
                    "height": 600,
                    "thumbnail": "base64-encoded-thumbnail",
                    "source": "web_scraping"
                }
            ],
            "skipped_images": 2,
            "errors": [],
            "url": "https://example.com"
        },
        "message": "Successfully scraped 5 images"
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        # Validate URL format (basic validation)
        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get optional parameters with defaults
        min_pixels = data.get('min_pixels', 100)
        num_images = data.get('num_images')  # None means no limit
        image_types = data.get('image_types')  # None means use defaults
        usage = data.get('usage', 'All Products')

        # Validate min_pixels
        if not isinstance(min_pixels, int) or min_pixels < 1:
            return jsonify({
                "success": False,
                "error": "min_pixels must be a positive integer"
            }), 400

        # Validate num_images if provided
        if num_images is not None and (not isinstance(num_images, int) or num_images < 1):
            return jsonify({
                "success": False,
                "error": "num_images must be a positive integer"
            }), 400

        # Validate image_types if provided
        if image_types is not None:
            if not isinstance(image_types, list) or not all(isinstance(t, str) for t in image_types):
                return jsonify({
                    "success": False,
                    "error": "image_types must be a list of strings"
                }), 400

            # Check against supported types
            supported_types = get_supported_image_types()
            invalid_types = [t for t in image_types if t.lower() not in supported_types]
            if invalid_types:
                return jsonify({
                    "success": False,
                    "error": f"Unsupported image types: {invalid_types}. Supported types: {supported_types}"
                }), 400

        # Validate URL accessibility (optional check)
        if not validate_url(url):
            return jsonify({
                "success": False,
                "error": "URL is not accessible or does not respond"
            }), 400

        # Process image scraping
        scraping_results = scrape_images(
            url=url,
            min_pixels=min_pixels,
            num_images=num_images,
            image_types=image_types,
            usage=usage
        )

        # Check if scraping was successful
        if not scraping_results.get('success', False):
            errors = scraping_results.get('errors', ['Unknown error occurred'])
            return jsonify({
                "success": False,
                "error": "Failed to scrape images",
                "details": errors,
                "data": scraping_results
            }), 500

        # Return successful response
        total_images = scraping_results.get('total_images', 0)
        message = f"Successfully scraped {total_images} image{'s' if total_images != 1 else ''}"

        return jsonify({
            "success": True,
            "data": scraping_results,
            "message": message
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/image-statistics', methods=['GET'])
@auth_required
def get_scraped_image_statistics():
    """
    GET API endpoint to retrieve statistics about scraped images.

    Query parameters:
    - usage_filter (optional): Filter statistics by usage category

    Returns:
    {
        "success": true,
        "data": {
            "total_images": 25,
            "usage_categories": {
                "Product Images": 15,
                "All Products": 10
            },
            "image_types": {
                "jpg": 12,
                "png": 8,
                "webp": 5
            },
            "size_distribution": {
                "small": 5,    // < 500px
                "medium": 12,  // 500-1000px
                "large": 8     // > 1000px
            },
            "source_distribution": {
                "web_scraping": 20,
                "manual_upload": 5,
                "other": 0
            }
        }
    }
    """
    try:
        # Get optional usage filter from query parameters
        usage_filter = request.args.get('usage_filter')

        # Get image statistics
        stats = get_image_statistics(usage_filter)

        # Check if there was an error in getting statistics
        if 'error' in stats:
            return jsonify({
                "success": False,
                "error": "Failed to retrieve image statistics",
                "details": stats['error']
            }), 500

        return jsonify({
            "success": True,
            "data": stats,
            "message": "Image statistics retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/scraping-info', methods=['GET'])
@auth_required
def get_scraping_info():
    """
    GET API endpoint to retrieve information about image scraping capabilities.

    Returns:
    {
        "success": true,
        "data": {
            "supported_image_types": ["jpg", "jpeg", "png", "webp", "gif"],
            "default_min_pixels": 100,
            "default_usage": "All Products"
        }
    }
    """
    try:
        return jsonify({
            "success": True,
            "data": {
                "supported_image_types": get_supported_image_types(),
                "default_min_pixels": 100,
                "default_usage": "All Products"
            },
            "message": "Scraping information retrieved successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500


@api_bp.route('/estimate-scraping', methods=['POST'])
@auth_required
def estimate_image_scraping():
    """
    POST API endpoint to estimate scraping time and image count for a URL.

    Expected JSON payload:
    {
        "url": "https://example.com"
    }

    Returns:
    {
        "success": true,
        "data": {
            "estimated_images": 25,
            "estimated_time_seconds": 50,
            "estimated_time_minutes": 0.8,
            "url": "https://example.com"
        }
    }
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        # Validate required fields
        url = data.get('url')
        if not url:
            return jsonify({
                "success": False,
                "error": "Missing required field: url"
            }), 400

        # Validate URL format
        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "success": False,
                "error": "Invalid URL format. URL must start with http:// or https://"
            }), 400

        # Get estimation
        estimation = estimate_scraping_time(url)

        # Check if estimation was successful
        if not estimation.get('success', False):
            return jsonify({
                "success": False,
                "error": "Failed to estimate scraping time",
                "details": estimation.get('error', 'Unknown error occurred'),
                "data": estimation
            }), 500

        return jsonify({
            "success": True,
            "data": estimation,
            "message": "Scraping estimation completed successfully"
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "details": str(e)
        }), 500
